<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Polismyndigheten | DurTvå</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #EFEFEF;
            color: #333;
            font-size: 14px;
        }
        .breadcrumbs {
            background-color: #d1d5db;
            color: #6b7280;
            font-size: 13px;
            padding: 8px 0;
        }
        .section-header {
            border: 1px solid #c5c8cc;
        }
        .content-area {
            background-color: #ffffff;
            border: 1px solid #c5c8cc;
        }
        .table-header {
            background-color: #e1e3e6;
        }
        .table-header th {
            cursor: pointer;
            padding: 4px 8px;
        }
        .table-header th:hover {
            background-color: #d1d3d6;
        }
        .table-highlight {
            background-color: #fffbe4;
            font-weight: 600;
        }
        .table-row td {
            padding: 1px 8px;
            cursor: pointer;
        }
        .table-row:hover {
            background-color: #f0f9ff;
        }
        input[type="text"], input[type="date"] {
            border: 1px solid #c5c8cc;
            padding: 4px 8px;
        }
        label {
            margin-bottom: 4px;
            display: block;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .cross-reference-btn {
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            font-weight: bold;
            border-radius: 4px;
            margin-top: 16px;
            width: 100%;
        }
        .cross-reference-btn:hover {
            background-color: #b91c1c;
        }
        .profile-image {
            width: 200px;
            height: 250px;
            background-color: #e5e7eb;
            border: 2px solid #9ca3af;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body class="min-h-screen">

    <!-- Header Image - Full Width -->
    <div class="w-full overflow-hidden">
        <img src="header.png" alt="Header Bild" class="block" style="width: auto; height: auto; min-width: 100%;">
    </div>

    <!-- Breadcrumbs -->
    <div class="breadcrumbs w-full">
        <div class="max-w-screen-2xl mx-auto px-4">
            /DurTvå/PMStockholm/Ärenden/
        </div>
    </div>

    <!-- Search View -->
    <div id="search-view" class="w-full max-w-screen-2xl mx-auto p-4">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Left Sidebar: Database Search -->
            <aside class="w-full md:w-1/4 lg:w-1/5">
                <div class="section-header p-2 font-bold bg-gray-600 text-white">
                    <h3><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" /></svg>Databasökning</h3>
                </div>
                <div class="content-area p-4">
                    <form id="search-form">
                        <!-- Personal/profil -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Personal/profil</legend>
                            <label for="namn">Namn:</label>
                            <input type="text" id="namn" name="namn" class="w-full mb-2">
                            <label>Kön:</label>
                            <div class="flex gap-4 mb-2">
                                <div><input type="radio" id="kon_alla" name="kon" value="" checked> <label for="kon_alla" class="inline">Alla</label></div>
                                <div><input type="radio" id="man" name="kon" value="Man"> <label for="man" class="inline">Man</label></div>
                                <div><input type="radio" id="kvinna" name="kon" value="Kvinna"> <label for="kvinna" class="inline">Kvinna</label></div>
                            </div>
                            <label>Ålder:</label>
                            <div class="flex items-center gap-2">
                                <input type="text" id="alder_fran" name="alder_fran" class="w-1/2" placeholder="Från">
                                <span>Till</span>
                                <input type="text" id="alder_till" name="alder_till" class="w-1/2" placeholder="Till">
                            </div>
                        </fieldset>

                        <!-- Tidsperiod -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Tidsperiod</legend>
                            <div><input type="radio" id="alla_ar" name="tidsperiod" value="" checked> <label for="alla_ar" class="inline">Alla år</label></div>
                            <div><input type="radio" id="senaste_5" name="tidsperiod" value="5"> <label for="senaste_5" class="inline">Senaste 5 åren</label></div>
                            <div><input type="radio" id="senaste_10" name="tidsperiod" value="10"> <label for="senaste_10" class="inline">Senaste 10 åren</label></div>
                            <div><input type="radio" id="senaste_15" name="tidsperiod" value="15"> <label for="senaste_15" class="inline">Senaste 15 åren</label></div>
                        </fieldset>

                        <button type="button" id="advanced-date-btn" class="w-full bg-gray-500 text-white p-2 mb-4 font-bold hover:bg-gray-600 transition-colors">
                            Avancerat datumfilter...
                        </button>

                        <button type="submit" class="w-full bg-blue-600 text-white p-2 mb-6 font-bold hover:bg-blue-700 transition-colors">SÖK</button>

                        <!-- Etnisk bakgrund -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Etnisk bakgrund</legend>
                            <div><input type="checkbox" id="svensk"> <label for="svensk" class="inline">Svensk bakgrund</label></div>
                            <div><input type="checkbox" id="utlandsk"> <label for="utlandsk" class="inline">Utländsk bakgrund</label></div>
                            <div><input type="checkbox" id="okand_etnisk"> <label for="okand_etnisk" class="inline">Okänd</label></div>
                        </fieldset>

                        <!-- Våld i tjänsten -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Våld i tjänsten</legend>
                            <div><input type="checkbox" id="vald_ja"> <label for="vald_ja" class="inline">Anklagad för våld</label></div>
                        </fieldset>

                        <!-- Anställningsform -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Anställningsform</legend>
                            <div><input type="checkbox" id="tillsvidare"> <label for="tillsvidare" class="inline">Tillsvidare</label></div>
                            <div><input type="checkbox" id="provanstallning"> <label for="provanstallning" class="inline">Provanställning</label></div>
                        </fieldset>
                        
                        <!-- Tjänst -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Tjänst</legend>
                            <div><input type="checkbox" id="yttre_befal"> <label for="yttre_befal" class="inline">Yttre befäl</label></div>
                            <div><input type="checkbox" id="utredning_tjanst"> <label for="utredning_tjanst" class="inline">Utredning</label></div>
                            <div><input type="checkbox" id="insatsstyrka"> <label for="insatsstyrka" class="inline">Insatsstyrka</label></div>
                        </fieldset>

                         <!-- Uniformerad personal -->
                        <fieldset class="mb-4">
                            <legend class="font-semibold mb-2">Uniformerad personal</legend>
                             <div><input type="radio" id="unif_ja" name="uniformerad"> <label for="unif_ja" class="inline">Ja</label></div>
                             <div><input type="radio" id="unif_nej" name="uniformerad"> <label for="unif_nej" class="inline">Nej</label></div>
                        </fieldset>
                        
                        <!-- Fällande dom -->
                        <fieldset>
                            <legend class="font-semibold mb-2">Fällande dom</legend>
                             <div><input type="radio" id="dom_ja" name="dom"> <label for="dom_ja" class="inline">Ja</label></div>
                             <div><input type="radio" id="dom_nej" name="dom"> <label for="dom_nej" class="inline">Nej</label></div>
                        </fieldset>
                    </form>
                </div>
            </aside>

            <!-- Right Side: Search Results -->
            <main class="w-full md:w-3/4 lg:w-4/5">
                <div class="section-header p-2 font-bold flex justify-between items-center bg-gray-600 text-white">
                    <h3><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M9 9a2 2 0 114 0 2 2 0 01-4 0z" /><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a4 4 0 100 8 4 4 0 000-8z" clip-rule="evenodd" /></svg>Sökresultat</h3>
                    <span id="result-count" class="text-sm font-normal bg-gray-500 text-white px-2 py-0.5"></span>
                </div>
                <div class="content-area overflow-x-auto">
                    <table class="w-full text-left">
                        <thead class="table-header">
                            <tr>
                                <th class="p-2" data-sort="efternamn">Efternamn</th>
                                <th class="p-2" data-sort="fornamn">Förnamn</th>
                                <th class="p-2">Personnummer</th>
                                <th class="p-2">Polis-ID</th>
                                <th class="p-2">Kodnr Väldsfall</th>
                                <th class="p-2" data-sort="artal">Årtal</th>
                            </tr>
                        </thead>
                        <tbody id="results-table-body">
                            <!-- Rows will be dynamically inserted here by JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Cross Reference Button (hidden by default) -->
                <button id="cross-reference-btn" class="cross-reference-btn hidden" onclick="crossReferenceWithDutyList()">
                    Korskör med tjänstgöringslista (Mordnätter)
                </button>
            </main>
        </div>
    </div>

    <!-- Personnel File View (hidden by default) -->
    <div id="personnel-view" class="w-full max-w-screen-2xl mx-auto p-4 hidden">
        <div class="flex flex-col md:flex-row gap-6">
            <!-- Profile Image -->
            <div class="w-full md:w-1/3">
                <div class="profile-image mx-auto">
                    Profilbild saknas
                </div>
            </div>
            
            <!-- Personnel Information -->
            <div class="w-full md:w-2/3">
                <div class="section-header p-2 font-bold bg-gray-600 text-white mb-4">
                    <h3>Personalakt</h3>
                </div>
                <div class="content-area p-6">
                    <div id="personnel-details" class="space-y-4">
                        <!-- Details will be populated by JavaScript -->
                    </div>
                    
                    <div class="flex gap-4 mt-6">
                        <button onclick="exportPersonnelFile()" class="bg-green-600 text-white px-6 py-2 font-bold hover:bg-green-700 transition-colors">
                            Exportera
                        </button>
                        <button onclick="backToSearchResults()" class="bg-gray-600 text-white px-6 py-2 font-bold hover:bg-gray-700 transition-colors">
                            Tillbaka till sökresultat
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script src="databas.js"></script>

<script>
    
    // --- EXPANDED MOCK DATABASE ---
    // allUsers is loaded from databas.js

    let currentSort = { key: 'efternamn', asc: true };
    let displayedUsers = [...allUsers];
    let savedSearchResults = [];
    let selectedDutyDates = [];

    // --- DOM ELEMENTS ---
    const tableBody = document.getElementById('results-table-body');
    const resultCount = document.getElementById('result-count');
    const searchForm = document.getElementById('search-form');
    const nameInput = document.getElementById('namn');
    const ageFromInput = document.getElementById('alder_fran');
    const ageToInput = document.getElementById('alder_till');
    const searchView = document.getElementById('search-view');
    const personnelView = document.getElementById('personnel-view');

    // Modal functionality
    const dateModal = document.getElementById('date-modal');
    const advancedDateBtn = document.getElementById('advanced-date-btn');
    const closeModalBtn = document.getElementById('close-modal');
    const addDateBtn = document.getElementById('add-date-btn');
    const applyFilterBtn = document.getElementById('apply-date-filter');
    const moveRightBtn = document.getElementById('move-right');
    const moveLeftBtn = document.getElementById('move-left');

    // Initialize date dropdowns
    function initializeDateDropdowns() {
        const yearSelect = document.getElementById('year-select');
        const monthSelect = document.getElementById('month-select');
        const daySelect = document.getElementById('day-select');

        // Populate years (2015-2025)
        for (let year = 2015; year <= 2025; year++) {
            yearSelect.innerHTML += `<option value="${year}">${year}</option>`;
        }

        // Populate months
        const months = ['Januari', 'Februari', 'Mars', 'April', 'Maj', 'Juni', 
                       'Juli', 'Augusti', 'September', 'Oktober', 'November', 'December'];
        months.forEach((month, index) => {
            monthSelect.innerHTML += `<option value="${(index + 1).toString().padStart(2, '0')}">${month}</option>`;
        });

        // Populate days
        for (let day = 1; day <= 31; day++) {
            daySelect.innerHTML += `<option value="${day.toString().padStart(2, '0')}">${day}</option>`;
        }
    }

    advancedDateBtn.addEventListener('click', () => {
        dateModal.classList.remove('hidden');
        initializeDateDropdowns();
    });

    closeModalBtn.addEventListener('click', () => {
        dateModal.classList.add('hidden');
    });

    addDateBtn.addEventListener('click', () => {
        const year = document.getElementById('year-select').value;
        const month = document.getElementById('month-select').value;
        const day = document.getElementById('day-select').value;

        if (year && month && day) {
            const dateString = `${year}-${month}-${day}`;
            const availableDates = document.getElementById('available-dates');
            
            // Check if date already exists
            const existingOptions = Array.from(availableDates.options);
            if (!existingOptions.some(option => option.value === dateString)) {
                const option = new Option(dateString, dateString);
                availableDates.add(option);
            }
        }
    });

    moveRightBtn.addEventListener('click', () => {
        const available = document.getElementById('available-dates');
        const selected = document.getElementById('selected-dates');
        
        Array.from(available.selectedOptions).forEach(option => {
            selected.add(new Option(option.text, option.value));
            available.removeChild(option);
        });
    });

    moveLeftBtn.addEventListener('click', () => {
        const available = document.getElementById('available-dates');
        const selected = document.getElementById('selected-dates');
        
        Array.from(selected.selectedOptions).forEach(option => {
            available.add(new Option(option.text, option.value));
            selected.removeChild(option);
        });
    });

    applyFilterBtn.addEventListener('click', () => {
        const selected = document.getElementById('selected-dates');
        selectedDutyDates = Array.from(selected.options).map(option => option.value);
        dateModal.classList.add('hidden');
        filterUsers(); // Trigger search with new date filter
    });

    // --- FUNCTIONS ---

    function renderTableAnimated(users) {
        tableBody.innerHTML = '';

        if (users.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="p-4 text-center text-gray-500">Inga resultat hittades.</td></tr>';
            resultCount.textContent = 'Visar 0 Resultat';
            return;
        }

        resultCount.textContent = `Hittade ${users.length} resultat...`;

        users.forEach((user, index) => {
            setTimeout(() => {
                const row = document.createElement('tr');
                let rowClass = 'border-gray-200 table-row';
                if (index % 2 !== 0) { rowClass += ' bg-blue-50'; }
                if (user.highlight) { rowClass += ' table-highlight'; }
                row.className = rowClass;

                row.innerHTML = `
                    <td class="p-2">${user.efternamn}</td>
                    <td class="p-2">${user.fornamn}</td>
                    <td class="p-2">${user.personnummer}</td>
                    <td class="p-2">${user.polisId}</td>
                    <td class="p-2">${user.kodnr}</td>
                    <td class="p-2">${user.artal}</td>
                `;
                
                row.addEventListener('click', () => showPersonnelFile(user));
                tableBody.appendChild(row);

                if (index === users.length - 1) {
                    resultCount.textContent = `Visar ${users.length} Resultat`;
                }
            }, index * 50);
        });
    }

    function filterUsers() {
        const nameFilter = nameInput.value.toLowerCase();
        const genderFilter = document.querySelector('input[name="kon"]:checked').value;
        const ageFrom = parseInt(ageFromInput.value) || 0;
        const ageTo = parseInt(ageToInput.value) || 999;
        const tidsperiodFilter = document.querySelector('input[name="tidsperiod"]:checked').value;
        
        const svenskChecked = document.getElementById('svensk').checked;
        const utlandskChecked = document.getElementById('utlandsk').checked;
        const okandEtniskChecked = document.getElementById('okand_etnisk').checked;
        const valdChecked = document.getElementById('vald_ja').checked;

        displayedUsers = allUsers.filter(user => {
            const fullName = `${user.fornamn} ${user.efternamn}`.toLowerCase();
            const birthYear = parseInt(user.personnummer.substring(0, 2));
            const currentYear = new Date().getFullYear().toString().substring(2);
            let age = (birthYear > currentYear) ? 100 - birthYear + parseInt(currentYear) : parseInt(currentYear) - birthYear;

            const nameMatch = nameFilter ? fullName.includes(nameFilter) : true;
            const genderMatch = genderFilter ? user.kon === genderFilter : true;
            const ageMatch = age >= ageFrom && age <= ageTo;
            
            // Time period filter
            let timeMatch = true;
            if (tidsperiodFilter) {
                const currentYear = new Date().getFullYear();
                const yearsBack = parseInt(tidsperiodFilter);
                timeMatch = user.artal >= (currentYear - yearsBack);
            }
            
            // Ethnic background filter
            let ethnicMatch = true;
            if (svenskChecked || utlandskChecked || okandEtniskChecked) {
                ethnicMatch = (svenskChecked && user.etniskBakgrund === "Svensk") ||
                             (utlandskChecked && user.etniskBakgrund === "Utländsk") ||
                             (okandEtniskChecked && user.etniskBakgrund === "Okänd");
            }
            
            // Violence filter
            const violenceMatch = valdChecked ? user.anklagadVald === true : true;
            
            // Duty date filter
            let dutyMatch = true;
            if (selectedDutyDates.length > 0 && user.tjanstgoringsperioder) {
                dutyMatch = selectedDutyDates.every(date => user.tjanstgoringsperioder.includes(date));
            }

            return nameMatch && genderMatch && ageMatch && timeMatch && ethnicMatch && violenceMatch && dutyMatch;
        });
        
        savedSearchResults = [...displayedUsers];
        sortUsers(currentSort.key, currentSort.asc, false);
    }

    function sortUsers(key, asc = true, toggle = true) {
        if (toggle && currentSort.key === key) {
            currentSort.asc = !currentSort.asc;
        } else {
            currentSort.key = key;
            currentSort.asc = asc;
        }

        displayedUsers.sort((a, b) => {
            const valA = a[key];
            const valB = b[key];
            let comparison = 0;
            if (valA > valB) { comparison = 1; } 
            else if (valA < valB) { comparison = -1; }
            return asc ? comparison : comparison * -1;
        });

        renderTableAnimated(displayedUsers);
    }

    function crossReferenceWithDutyList() {
        // Filter current results to only show those on duty during murder nights
        displayedUsers = displayedUsers.filter(user => user.iTjanstMordnatter === true);
        renderTableAnimated(displayedUsers);
    }

    function showPersonnelFile(user) {
        // Hide search view and show personnel view
        searchView.classList.add('hidden');
        personnelView.classList.remove('hidden');
        
        // Populate personnel details
        const detailsDiv = document.getElementById('personnel-details');
        detailsDiv.innerHTML = `
            <div class="grid grid-cols-2 gap-4">
                <div><strong>Namn:</strong> ${user.fornamn} ${user.efternamn}</div>
                <div><strong>Personnummer:</strong> ${user.personnummer}</div>
                <div><strong>Polis-ID:</strong> ${user.polisId}</div>
                <div><strong>Anställningsdatum:</strong> ${user.anstallningsdatum}</div>
                <div><strong>Kön:</strong> ${user.kon}</div>
                <div><strong>Etnisk bakgrund:</strong> ${user.etniskBakgrund}</div>
                <div><strong>Anklagad för våld:</strong> ${user.anklagadVald ? 'Ja' : 'Nej'}</div>
                <div><strong>Kodnr Väldsfall:</strong> ${user.kodnr}</div>
                <div><strong>Årtal:</strong> ${user.artal}</div>
            </div>
        `;
    }

    function exportPersonnelFile() {
        alert('Personalakt exporteras...');
    }

    function backToSearchResults() {
        // Hide personnel view and show search view
        personnelView.classList.add('hidden');
        searchView.classList.remove('hidden');
        
        // Restore previous search results
        displayedUsers = [...savedSearchResults];
        renderTableAnimated(displayedUsers);
    }

    // --- EVENT LISTENERS ---

    searchForm.addEventListener('submit', (event) => {
        event.preventDefault();
        const searchButton = searchForm.querySelector('button[type="submit"]');

        searchButton.disabled = true;
        searchButton.textContent = 'SÖKER...';
        tableBody.innerHTML = '<tr><td colspan="6" class="p-4 text-center text-gray-500">Söker...</td></tr>';
        resultCount.textContent = '';

        setTimeout(() => {
            filterUsers();
            searchButton.disabled = false;
            searchButton.textContent = 'SÖK';
        }, 500);
    });

    document.querySelectorAll('.table-header th[data-sort]').forEach(th => {
        th.addEventListener('click', () => {
            const sortKey = th.getAttribute('data-sort');
            sortUsers(sortKey);
        });
    });

    // --- INITIAL RENDER ---
    sortUsers('efternamn', true, false);
</script>

</body>
</html>

